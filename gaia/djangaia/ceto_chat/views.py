from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json
import asyncio
import logging

# Import MCP client library
try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False

logger = logging.getLogger(__name__)


def ceto_chat_view(request):
    """
    Renders the Ceto Chat base template.
    """
    return render(request, 'ceto_chat/ceto_chat_base.html')


@csrf_exempt
@require_http_methods(["GET"])
def list_mcp_tools(request):
    """
    List available MCP tools from the running server.
    Level 0031: Connect to MCP server
    """
    if not MCP_AVAILABLE:
        return JsonResponse({
            'error': 'MCP client library not available',
            'tools': []
        }, status=500)

    try:
        # Create MCP client and connect to default server
        def debug_callback(level, msg, data=None):
            logger.info(f"MCP Debug [{level}]: {msg}")

        client = MCPClientLib(debug_callback=debug_callback)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Connect to the MCP server (same as running server)
            server_url = "http://0.0.0.0:9000/mcp"
            success = loop.run_until_complete(client.connect_to_server(server_url))

            if not success:
                return JsonResponse({
                    'error': f'Failed to connect to MCP server at {server_url}',
                    'tools': []
                }, status=500)

            # Get available tools
            tools = client.available_tools

            # Format tools for frontend
            formatted_tools = []
            for tool in tools:
                formatted_tools.append({
                    'name': tool.get('name', 'unknown'),
                    'description': tool.get('description', 'No description'),
                    'input_schema': tool.get('input_schema', {})
                })

            return JsonResponse({
                'success': True,
                'tools': formatted_tools,
                'server_url': server_url,
                'tool_count': len(formatted_tools)
            })

        finally:
            # Cleanup
            loop.run_until_complete(client.cleanup())
            loop.close()

    except Exception as e:
        logger.error(f"Error listing MCP tools: {e}")
        return JsonResponse({
            'error': f'MCP client error: {str(e)}',
            'tools': []
        }, status=500)
